# Home Credit Default Risk - 综合配置文件
# 整合所有模块的配置参数

# 项目基本信息
project:
  name: "home_credit_default_risk"
  version: "2.0.0"
  description: "Home Credit Default Risk Prediction - 重构版"
  author: "Augment Agent"
  created_date: "2024-01-01"

# 环境配置
environment:
  # Python解释器路径
  python_interpreter: "D:/Anaconda/customenvs/trainer/python.exe"
  
  # 工作目录
  working_directory: "./projects/home_credit_default_risk"
  
  # 随机种子（全局）
  random_seed: 42
  
  # 日志级别
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  
  # 是否启用GPU（如果可用）
  use_gpu: false

# 数据配置
data:
  # 数据路径
  paths:
    # 主数据文件
    application_train: '../../data/raw/home-credit-default-risk/application_train.csv'
    application_test: '../../data/raw/home-credit-default-risk/application_test.csv'
    
    # 辅助数据文件
    bureau: '../../data/raw/home-credit-default-risk/bureau.csv'
    bureau_balance: '../../data/raw/home-credit-default-risk/bureau_balance.csv'
    previous_application: '../../data/raw/home-credit-default-risk/previous_application.csv'
    pos_cash_balance: '../../data/raw/home-credit-default-risk/POS_CASH_balance.csv'
    credit_card_balance: '../../data/raw/home-credit-default-risk/credit_card_balance.csv'
    installments_payments: '../../data/raw/home-credit-default-risk/installments_payments.csv'
  
  # 数据验证
  validation:
    enable: true
    check_integrity: true
    check_missing: true
    check_duplicates: true
    required_columns:
      application_train: ['SK_ID_CURR', 'TARGET']
      application_test: ['SK_ID_CURR']
  
  # 数据清洗
  cleaning:
    missing_values:
      drop_threshold: 0.95
      numerical_strategy: 'median'
      categorical_strategy: 'mode'
      create_missing_indicators: true
    outliers:
      detection_method: 'iqr'
      treatment: 'clip'
    duplicates:
      drop_duplicates: false
  
  # 内存优化
  memory_optimization:
    enable: true
    reduce_dtypes: true
    monitor_memory: true
  
  # 数据分割
  split:
    validation_size: 0.2
    random_state: 42
    stratify: true
    stratify_column: 'TARGET'

# 特征工程配置
features:
  # 基础特征处理
  basic_processing:
    # 数值特征
    numerical:
      scaling:
        enable: false  # 树模型通常不需要缩放
        method: 'standard'  # 'standard', 'minmax', 'robust'
      
      transformations:
        log_transform:
          enable: true
          columns: ['AMT_INCOME_TOTAL', 'AMT_CREDIT', 'AMT_ANNUITY', 'AMT_GOODS_PRICE']
        
        polynomial:
          enable: false
          degree: 2
          interaction_only: true
    
    # 类别特征
    categorical:
      encoding:
        method: 'label'  # 'label', 'onehot', 'target'
        handle_unknown: 'ignore'
      
      rare_categories:
        threshold: 0.01  # 低于此频率的类别合并为'rare'
        enable: true
  
  # 高级特征工程
  advanced:
    # 聚合特征
    aggregation:
      enable: true
      tables_to_aggregate: ['bureau', 'previous_application', 'credit_card_balance', 'pos_cash_balance', 'installments_payments']
      aggregation_functions: ['min', 'max', 'mean', 'std', 'sum', 'count']
    
    # 交叉特征
    interactions:
      enable: true
      # EXT_SOURCE组合
      ext_source_combinations: true
      # 重要特征交叉
      important_pairs:
        - ['AMT_CREDIT', 'AMT_INCOME_TOTAL']
        - ['AMT_ANNUITY', 'AMT_INCOME_TOTAL']
        - ['AMT_CREDIT', 'AMT_ANNUITY']
    
    # 领域知识特征
    domain_features:
      enable: true
      # 信贷比率
      credit_ratios: true
      # 年龄和就业特征
      demographic_features: true
      # EXT_SOURCE统计特征
      ext_source_stats: true
    
    # 聚类特征
    clustering:
      enable: true
      n_clusters: [3, 5, 8]
      features_for_clustering: ['AMT_INCOME_TOTAL', 'AMT_CREDIT', 'AMT_ANNUITY', 'EXT_SOURCE_MEAN']
  
  # 特征选择
  selection:
    enable: true
    methods:
      # 过滤法
      filter:
        enable: true
        correlation_threshold: 0.98  # 移除高相关特征
        variance_threshold: 0.01     # 移除低方差特征
      
      # 包裹法
      wrapper:
        enable: false
        method: 'rfe'  # 'rfe', 'sfs'
        n_features: 500
      
      # 嵌入法
      embedded:
        enable: true
        method: 'lightgbm'  # 'lightgbm', 'xgboost'
        importance_threshold: 0.001
    
    # 最大特征数
    max_features: 800

# 模型配置
models:
  # 问题类型
  problem_type: "binary_classification"
  
  # 评估指标
  primary_metric: "roc_auc"
  secondary_metrics: ["accuracy", "precision", "recall", "f1"]
  
  # 交叉验证
  cross_validation:
    method: "stratified_kfold"
    n_splits: 5
    shuffle: true
    random_state: 42
  
  # 基线模型
  baseline:
    enable: true
    models: ["logistic_regression", "decision_tree", "random_forest"]
    quick_evaluation: true
  
  # 高级模型
  advanced:
    # LightGBM
    lightgbm:
      enable: true
      params:
        objective: "binary"
        metric: "auc"
        boosting_type: "gbdt"
        num_leaves: 63
        learning_rate: 0.01
        feature_fraction: 0.8
        bagging_fraction: 0.8
        bagging_freq: 5
        min_child_samples: 20
        reg_alpha: 0.1
        reg_lambda: 0.1
        random_state: 42
        n_jobs: -1
        verbosity: -1
        n_estimators: 3000
        max_depth: 8
      early_stopping_rounds: 200
    
    # XGBoost
    xgboost:
      enable: true
      params:
        objective: "binary:logistic"
        eval_metric: "auc"
        max_depth: 7
        learning_rate: 0.01
        n_estimators: 3000
        subsample: 0.8
        colsample_bytree: 0.8
        reg_alpha: 0.1
        reg_lambda: 0.1
        random_state: 42
        n_jobs: -1
        verbosity: 0
      early_stopping_rounds: 200
    
    # CatBoost
    catboost:
      enable: false  # 可选启用
      params:
        objective: "Logloss"
        eval_metric: "AUC"
        iterations: 3000
        learning_rate: 0.01
        depth: 8
        l2_leaf_reg: 3
        random_seed: 42
        verbose: false
      early_stopping_rounds: 200
  
  # 集成模型
  ensemble:
    enable: true
    
    # 投票法
    voting:
      enable: true
      voting_type: "soft"  # 'hard', 'soft'
    
    # 平均法
    averaging:
      enable: true
      method: "weighted"  # 'simple', 'weighted'
      auto_weights: true  # 基于CV分数自动计算权重
    
    # 堆叠法
    stacking:
      enable: true
      meta_model: "logistic_regression"
      cv_folds: 5
      meta_model_params:
        C: 1.0
        penalty: "l2"
        solver: "liblinear"
        max_iter: 1000
        random_state: 42

# 超参数优化配置
hyperparameter_tuning:
  enable: false  # 默认关闭，需要时启用
  
  # 优化方法
  method: "optuna"  # 'optuna', 'grid_search', 'random_search'
  
  # 优化参数
  n_trials: 100
  timeout: 3600  # 秒
  n_jobs: -1
  
  # 要优化的模型
  models_to_tune: ["lightgbm", "xgboost"]
  
  # 搜索空间（示例）
  search_spaces:
    lightgbm:
      num_leaves: [10, 300]
      learning_rate: [0.01, 0.3]
      max_depth: [3, 12]
      reg_alpha: [0.0, 1.0]
      reg_lambda: [0.0, 1.0]
    
    xgboost:
      max_depth: [3, 12]
      learning_rate: [0.01, 0.3]
      reg_alpha: [0.0, 1.0]
      reg_lambda: [0.0, 1.0]

# 输出配置
output:
  # 输出目录
  base_dir: "./outputs"
  
  # 子目录
  subdirs:
    models: "models"
    reports: "reports"
    plots: "plots"
    submissions: "submissions"
    logs: "logs"
    cache: "cache"
  
  # 文件命名
  naming:
    use_timestamp: true
    timestamp_format: "%Y%m%d_%H%M%S"
  
  # 保存格式
  formats:
    models: "pickle"  # 'pickle', 'joblib'
    data: "parquet"   # 'parquet', 'csv', 'pickle'
    plots: "png"      # 'png', 'pdf', 'svg'
  
  # 提交文件
  submission:
    filename: "submission.csv"
    columns: ["SK_ID_CURR", "TARGET"]

# 监控和日志配置
monitoring:
  # 性能监控
  performance:
    enable: true
    monitor_memory: true
    monitor_time: true
    log_interval: 100  # 每100步记录一次
  
  # 模型监控
  model:
    enable: true
    save_feature_importance: true
    save_learning_curves: true
    save_validation_curves: true
  
  # 日志配置
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_rotation: true
    max_file_size: "10MB"
    backup_count: 5

# 实验配置
experiments:
  # 实验跟踪
  tracking:
    enable: false
    backend: "mlflow"  # 'mlflow', 'wandb', 'tensorboard'
    experiment_name: "home_credit_default_risk"
  
  # 实验参数
  parameters:
    track_hyperparameters: true
    track_metrics: true
    track_artifacts: true
    track_model: true

# 部署配置（可选）
deployment:
  # 模型服务
  serving:
    enable: false
    framework: "flask"  # 'flask', 'fastapi', 'streamlit'
    host: "localhost"
    port: 5000
  
  # 模型版本管理
  versioning:
    enable: false
    backend: "mlflow"
    model_name: "home_credit_model"
